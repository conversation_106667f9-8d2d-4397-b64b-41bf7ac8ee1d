import discord
from discord.ui import View, Button, button
from discord.ext import commands

import psutil
import os
import sys

from utils.modules.ui.CreateEmbed import create_embed, get_text
from utils.constants import InterchatConstants
from utils.utils import check_user

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from main import Bot

class AboutButtons(View):
    def __init__(self, bot, language: str):
        super().__init__()
        self.bot: Bot = bot
        self.language = 'en'

        self.add_item(Button(emoji=bot.emotes.topggSparkles, label=get_text(language, 'commands.about.buttons.vote'), url='https://top.gg/bot/769921109209907241/vote', style=discord.ButtonStyle.link, row=2))
        self.add_item(Button(emoji=bot.emotes.bot_icon, label=get_text(language, 'commands.about.buttons.invite'), url='https://discord.com/application-directory/769921109209907241', style=discord.ButtonStyle.link))
        self.add_item(Button(emoji=bot.emotes.wand_icon, label=get_text(language, 'commands.about.buttons.dashboard'), url='https://interchat.tech/dashboard', style=discord.ButtonStyle.link))
        self.add_item(Button(emoji=bot.emotes.code_icon, label=get_text(language, 'commands.about.buttons.support'), url='https://discord.gg/8DhUA4HNpD', style=discord.ButtonStyle.link))

class StatButtons(View):
    def __init__(self, bot, language, constants):
        super().__init__()
        self.bot: Bot = bot
        self.language = 'en'
        self.constants = constants

        self.add_item(Button(emoji=bot.emotes.bot_icon, label=get_text(language, 'commands.about.buttons.invite'), url='https://discord.com/application-directory/769921109209907241', style=discord.ButtonStyle.link))
        self.add_item(Button(emoji=bot.emotes.wand_icon, label=get_text(language, 'commands.about.buttons.dashboard'), url='https://interchat.tech/dashboard', style=discord.ButtonStyle.link))
        
        shard_info = Button(emoji=bot.emotes.gear_icon, label='Shard Info', style=discord.ButtonStyle.grey)
        shard_info.callback = self.shard_info_callback
        self.add_item(shard_info)

    async def shard_info_callback(self, interaction: discord.Interaction):
        await interaction.response.defer()

        embed = discord.Embed(title='Shard Information', description=' ', color=self.constants.color())

        for shard_id, shard in self.bot.shards.items():
            guild_count = sum(1 for g in self.bot.guilds if g.shard_id == shard_id)
            latency = round(shard.latency * 1000)
            status = "Ready" if latency < 1000 else "Provisioning..."
            
            embed.add_field(name=f"Shard #{shard_id}: {status}", value=f'```Ping: {latency}ms\nGuilds: {guild_count}```', inline=True)

        current_shard = interaction.guild.shard_id if interaction.guild else 0
        embed.set_footer(text=f"Current Shard: #{current_shard}")

        await interaction.followup.send(embed=embed, ephemeral=True)

class General(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = InterchatConstants()

    @commands.hybrid_command(name='about', description='🚀 Learn how InterChat helps grow Discord communities', extras={'category': 'General'})
    @check_user()
    async def about(self, ctx: commands.Context):
        view = AboutButtons(self.bot, language='en')
        embed = create_embed(language='en', title_key='commands.about.title', description_key='commands.about.description_text', fields=[{'name_key': 'commands.about.features.title', 'value_key': 'commands.about.features.list'}], footer_key='commands.about.support_text')
        await ctx.send(embed=embed, view=view)

    @commands.hybrid_command(name='stats', description='📊 View InterChat\'s statistics.', extras={'category': 'General'})
    @check_user()
    async def stats(self, ctx: commands.Context):
        try:
            py_version = f'{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}'
            process = psutil.Process()
            ram_usage = process.memory_info().rss / (1024 ** 2)
        except Exception:
            py_version = 'N/A'
            ram_usage = 'N/A'

        embed = discord.Embed(title='InterChat Metrics', description=' ', color=self.constants.color())
        embed.add_field(name=f'{self.bot.emotes.bot_icon}  Bot', value=f'> **Uptime:** <t:{int(self.bot.start_time.timestamp())}:R>\n> **Servers:** {len(self.bot.guilds)}\n> **Members:** {sum([i.member_count for i in self.bot.guilds])}', inline=True)
        embed.add_field(name=f'{self.bot.emotes.gear_icon}  System', value=f"> **OS:** {os.name}\n> **Python:** {py_version}\n> **CPU Cores:** {os.cpu_count()}\n> **RAM Usage:** {ram_usage:.0f}mb", inline=True)
        embed.set_footer(text=f'InterChat | Version {self.constants.version()}')
        view = StatButtons(self.bot, 'en', self.constants)
        await ctx.send(embed=embed, view=view)
    
async def setup(bot):
    await bot.add_cog(General(bot))