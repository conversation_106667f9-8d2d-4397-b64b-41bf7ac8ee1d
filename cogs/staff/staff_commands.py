import discord
from discord.ext import commands
from sqlalchemy import select

from utils.modules.core.checks import is_interchat_staff_check
from utils.modules.core.db.models import <PERSON><PERSON>, User, ServerData
from utils.constants import InterchatConstants

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from main import Bot

class Staff(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = InterchatConstants()

    @commands.group()
    async def get(self, ctx: commands.Context):
        pass

    @get.command()
    @is_interchat_staff_check()
    async def hub(self, ctx: commands.Context, hub: str):
        try:
            async with ctx.bot.db.get_session() as session:
                stmt = select(Hub).where(Hub.name == str(hub))
                res = (await session.execute(stmt)).scalar()

            if not res:
                await ctx.send("Hub not found.")
                return

            embed = discord.Embed(color=self.constants.color())
            
            if res.iconUrl and res.iconUrl != 'none':
                embed.set_thumbnail(url=res.iconUrl)
            
            hub_title = f"**{res.name}**"
            if res.shortDescription:
                hub_title += f"\n{res.shortDescription}"
            embed.add_field(name=f"{self.bot.emotes.hash_icon} Name", value=hub_title, inline=True)
            
            embed.add_field(name=f"{self.bot.emotes.chat_icon} Messages", value=f"**{res.weeklyMessageCount}** this week", inline=True)
            embed.add_field(name=f"{self.bot.emotes.link_icon} Connections", value=f"**{len(res.connections)}** active", inline=True)
            
            embed.add_field(name=f"{self.bot.emotes.globe_icon} Created", value=f"<t:{int(res.createdAt.timestamp())}:D>\n<t:{int(res.createdAt.timestamp())}:R>", inline=True)
            embed.add_field(name=f"{self.bot.emotes.refresh_icon} Last Active", value=f"<t:{int(res.lastActive.timestamp())}:D>\n<t:{int(res.lastActive.timestamp())}:R>", inline=True)
            embed.add_field(name=f"{self.bot.emotes.trophy_icon} Upvotes", value=f"**{len(res.upvotes)}** total", inline=True)

            embed.add_field(name=f"{self.bot.emotes.person_icon} Owner", value=f"<@{res.ownerId}>", inline=True)
            
            location_info = []
            if res.language: location_info.append(f"{self.bot.emotes.globe_icon} {res.language}")
            if res.region: location_info.append(f"{self.bot.emotes.house_icon} {res.region}")
            if location_info:
                embed.add_field(name=f"{self.bot.emotes.globe_icon} Location", value=" • ".join(location_info), inline=True)
            else:
                embed.add_field(name=f"{self.bot.emotes.globe_icon} Location", value="Not specified", inline=True)
            
            embed.add_field(name=f"{self.bot.emotes.fire_icon} Activity", value=res.activityLevel.value.title(), inline=True)

            status_badges = []
            if res.verified: status_badges.append(f"{self.bot.emotes.tick} Verified")
            if res.partnered: status_badges.append(f"{self.bot.emotes.connect} Partnered") 
            if res.featured: status_badges.append(f"{self.bot.emotes.star} Featured")
            if res.private: status_badges.append(f"{self.bot.emotes.lock_icon} Private")
            if res.locked: status_badges.append(f"{self.bot.emotes.no} Locked")
            if res.nsfw: status_badges.append(f"{self.bot.emotes.alert_icon} NSFW")
            
            if status_badges:
                embed.add_field(name=f"Status", value=" • ".join(status_badges), inline=False)

            if res.moderators or res.blockWords or res.antiSwearRules or res.rules:
                mod_summary = []
                if res.moderators: mod_summary.append(f"{len(res.moderators)} mods")
                if res.rules: mod_summary.append(f"{len(res.rules)} rules")
                if res.blockWords: mod_summary.append(f"{len(res.blockWords)} blocked words")
                if res.antiSwearRules: mod_summary.append(f"{len(res.antiSwearRules)} anti-swear rules")
                
                embed.add_field(name=f"{self.bot.emotes.hammer_icon} Moderation", value=" • ".join(mod_summary), inline=False)

            if res.appealCooldownHours != 168:
                embed.add_field(name=f"{self.bot.emotes.clock_icon} Appeal Cooldown", value=f"{res.appealCooldownHours} hours", inline=True)

            if res.welcomeMessage:
                welcome_preview = res.welcomeMessage[:150] + ("..." if len(res.welcomeMessage) > 150 else "")
                embed.add_field(name=f"{self.bot.emotes.wave_anim} Welcome Message", value=f"```{welcome_preview}```", inline=False)

            if res.description and res.description != res.shortDescription and len(res.description) > 100:
                desc_preview = res.description[:200] + ("..." if len(res.description) > 200 else "")
                embed.add_field(name=f"{self.bot.emotes.description} Description", value=desc_preview, inline=False)

            if res.bannerUrl:
                embed.set_image(url=res.bannerUrl)

            embed.set_footer(text=f"Hub ID: {res.id}")

            await ctx.send(embed=embed)
        except Exception as e:
            print(e)

    @get.command()
    @is_interchat_staff_check()
    async def server(self, ctx: commands.Context, server_id: str):
        try:
            async with ctx.bot.db.get_session() as session:
                stmt = select(ServerData).where(ServerData.id == str(server_id))
                res = (await session.execute(stmt)).scalar()

            if not res:
                await ctx.send("Server not found.")
                return

            embed = discord.Embed(color=self.constants.color())
            
            if res.iconUrl:
                embed.set_thumbnail(url=res.iconUrl)
            
            embed.add_field(name=f"{self.bot.emotes.hash_icon} Name", value=f"**{res.name}**", inline=True)
            embed.add_field(name=f"{self.bot.emotes.ID_icon} Server ID", value=f"`{res.id}`", inline=True)
            embed.add_field(name=f"{self.bot.emotes.invite_icon} Invite Code", value=f"`{res.inviteCode}`", inline=True)
            
            embed.add_field(name=f"{self.bot.emotes.chat_icon} Messages", value=f"**{res.messageCount}** total", inline=True)
            embed.add_field(name=f"{self.bot.emotes.link_icon} Connections", value=f"**{len(res.connections)}** active", inline=True)
            
            premium_status = f"{self.bot.emotes.globe_icon} Premium" if res.premiumStatus else f"{self.bot.emotes.dot} Standard"
            embed.add_field(name=f"{self.bot.emotes.trophy_icon} Status", value=premium_status, inline=True)
            
            embed.add_field(name=f"{self.bot.emotes.calendar_icon} Created", value=f"<t:{int(res.createdAt.timestamp())}:D>\n<t:{int(res.createdAt.timestamp())}:R>", inline=True)
            embed.add_field(name=f"{self.bot.emotes.clock_icon} Last Message", value=f"<t:{int(res.lastMessageAt.timestamp())}:D>\n<t:{int(res.lastMessageAt.timestamp())}:R>", inline=True)
            embed.add_field(name=f"{self.bot.emotes.refresh_icon} Updated", value=f"<t:{int(res.updatedAt.timestamp())}:D>\n<t:{int(res.updatedAt.timestamp())}:R>", inline=True)

            mod_summary = []
            if res.infractions: mod_summary.append(f"{len(res.infractions)} infractions")
            if res.serverBans: mod_summary.append(f"{len(res.serverBans)} bans")
            
            if mod_summary:
                embed.add_field(name=f"Moderation", value=" • ".join(mod_summary), inline=False)

            if res.leaderboardEntries:
                embed.add_field(name=f"{self.bot.emotes.leaderboard_icon} Leaderboard", value=f"**{len(res.leaderboardEntries)}** entries", inline=True)

            embed.set_footer(text=f"Server ID: {res.id}")
            await ctx.send(embed=embed)
        except Exception as e:
            print(e)

    @get.command()
    @is_interchat_staff_check()
    async def user(self, ctx: commands.Context, user: discord.User):
        try:
            async with ctx.bot.db.get_session() as session:
                stmt = select(User).where(User.id == str(user.id))
                res = (await session.execute(stmt)).scalar()

            if not res:
                await ctx.send("User not found.")
                return

            embed = discord.Embed(color=self.constants.color())
            
            if res.image:
                embed.set_thumbnail(url=res.image)
            
            embed.add_field(name=f"{self.bot.emotes.person_icon} Name", value=f"**{res.name}**", inline=True)
            embed.add_field(name=f"{self.bot.emotes.ID_icon} User ID", value=f"`{res.id}`", inline=True)
            
            staff_status = f"{self.bot.emotes.staff_badge} Staff" if res.isStaff else f"{self.bot.emotes.dot} Member"
            embed.add_field(name=f"{self.bot.emotes.info_icon} Status", value=staff_status, inline=True)
            
            embed.add_field(name=f"{self.bot.emotes.chat_icon} Messages", value=f"**{res.messageCount}** sent", inline=True)
            embed.add_field(name=f"{self.bot.emotes.trophy_icon} Reputation", value=f"**{res.reputation}** points", inline=True)
            embed.add_field(name=f"{self.bot.emotes.globe_icon} Votes", value=f"**{res.voteCount}** cast", inline=True)
            
            embed.add_field(name=f"{self.bot.emotes.link_icon} Hub Joins", value=f"**{res.hubJoinCount}** total", inline=True)
            embed.add_field(name=f"{self.bot.emotes.fire_icon} Engagement", value=f"**{res.hubEngagementScore:.1f}**", inline=True)
            embed.add_field(name=f"{self.bot.emotes.globe_icon} Locale", value=f"**{res.locale.upper()}**", inline=True)

            embed.add_field(name=f"{self.bot.emotes.calendar_icon} Created", value=f"<t:{int(res.createdAt.timestamp())}:D>\n<t:{int(res.createdAt.timestamp())}:R>", inline=True)
            embed.add_field(name=f"{self.bot.emotes.clock_icon} Last Message", value=f"<t:{int(res.lastMessageAt.timestamp())}:D>\n<t:{int(res.lastMessageAt.timestamp())}:R>", inline=True)
            
            if res.lastVoted:
                embed.add_field(name=f"{self.bot.emotes.globe_icon} Last Vote", value=f"<t:{int(res.lastVoted.timestamp())}:D>\n<t:{int(res.lastVoted.timestamp())}:R>", inline=True)
            else:
                embed.add_field(name=f"{self.bot.emotes.globe_icon} Last Vote", value="Never", inline=True)

            preferences = []
            if res.showBadges: preferences.append("Show badges")
            if res.mentionOnReply: preferences.append("Mention on reply")
            if res.showNsfwHubs: preferences.append("Show NSFW hubs")
            
            if preferences:
                embed.add_field(name=f"Preferences", value=" • ".join(preferences), inline=False)

            owned_summary = []
            if res.ownedHubs: owned_summary.append(f"{len(res.ownedHubs)} hubs")
            if res.modPositions: owned_summary.append(f"{len(res.modPositions)} mod positions")
            if res.blockWordsCreated: owned_summary.append(f"{len(res.blockWordsCreated)} blocked words")
            if res.antiSwearRulesCreated: owned_summary.append(f"{len(res.antiSwearRulesCreated)} anti-swear rules")
            
            if owned_summary:
                embed.add_field(name=f"Created Content", value=" • ".join(owned_summary), inline=False)

            mod_summary = []
            if res.infractions: mod_summary.append(f"{len(res.infractions)} infractions received")
            if res.issuedInfractions: mod_summary.append(f"{len(res.issuedInfractions)} infractions issued")
            if res.bans: mod_summary.append(f"{len(res.bans)} bans received")
            if res.issuedBans: mod_summary.append(f"{len(res.issuedBans)} bans issued")
            
            if mod_summary:
                embed.add_field(name=f"{self.bot.emotes.hammer_icon} Moderation", value=" • ".join(mod_summary), inline=False)

            activity_summary = []
            if res.appeals: activity_summary.append(f"{len(res.appeals)} appeals")
            if res.reviews: activity_summary.append(f"{len(res.reviews)} reviews")
            if res.reportsSubmitted: activity_summary.append(f"{len(res.reportsSubmitted)} reports made")
            if res.reportsReceived: activity_summary.append(f"{len(res.reportsReceived)} reports received")
            if res.achievements: activity_summary.append(f"{len(res.achievements)} achievements")
            
            if activity_summary:
                embed.add_field(name=f"{self.bot.emotes.activities} Activity", value=" • ".join(activity_summary), inline=False)

            if res.donationTier:
                donation_text = f"Tier: {res.donationTier}"
                if res.donationExpiresAt:
                    donation_text += f"\nExpires: <t:{int(res.donationExpiresAt.timestamp())}:R>"
                embed.add_field(name=f"{self.bot.emotes.globe_icon} Donation", value=donation_text, inline=True)

            if res.badges:
                badge_text = " • ".join([badge.name for badge in res.badges])
                embed.add_field(name=f"{self.bot.emotes.staff_badge} Badges", value=badge_text, inline=False)

            embed.set_footer(text=f"User ID: {res.id}")
            await ctx.send(embed=embed)
        except Exception as e:
            print(e)

    @commands.command()
    @is_interchat_staff_check()
    async def blacklist(self, ctx: commands.Context):
        ...
        
async def setup(bot):
    await bot.add_cog(Staff(bot))