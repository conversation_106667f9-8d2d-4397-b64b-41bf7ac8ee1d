import discord
from discord.ext import commands

from utils.modules.errors.error_handler import error_handler

class OnCommandError(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
    
    @commands.Cog.listener()
    async def on_command_error(self, ctx: commands.Context, error):
        await error_handler(self.bot, ctx, error)

async def setup(bot):
    await bot.add_cog(OnCommandError(bot))