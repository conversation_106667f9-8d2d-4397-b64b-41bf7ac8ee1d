from collections.abc import Sequence
import aiohttp
import discord
from discord.ext import commands
from typing import TYPE_CHECKING
from sqlalchemy import select
from utils.modules.core.db.models import Connection, Hub
from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot


class OnMessageCreate(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        if message.author.bot or message.author.system:
            return

        # check db to see if message was sent in connected channel or not
        async with self.bot.db.get_session() as session:
            # Single optimized query using JOIN to get all needed data at once
            stmt = (
                select(Connection, Hub)
                .join(Hub, Connection.hubId == Hub.id)
                .where(
                    Connection.channelId == str(message.channel.id),
                    Connection.connected == True,
                )
            )

            result = await session.execute(stmt)
            row = result.first()

            if row is None:
                return

            connection, hub = row
            logger.info(f"Message from {message.author}: {message.content}")

            # Get all other connections in the same hub (excluding current channel)
            other_connections_stmt = select(Connection).where(
                Connection.hubId == hub.id,
                Connection.channelId != str(message.channel.id),
                Connection.connected == True,
            )
            result = await session.execute(other_connections_stmt)
            other_connections = result.scalars().all()

            if not other_connections:
                logger.info(f"No other connections found for hub {hub.id}")
                return

            try:
                # 4. broadcast message to other connections
                await self.broadcast_message(
                    message, connection, hub, other_connections
                )
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")

    async def broadcast_message(
        self,
        message: discord.Message,
        connection: Connection,
        hub: Hub,
        other_connections: Sequence[Connection],
    ):
        for other_conn in other_connections:
            async with aiohttp.ClientSession() as session:

                # Check if the webhook is already cached
                webhook = discord.Webhook.from_url(
                    url=other_conn.webhookUrl, session=session
                )

                # If the connection has a parentId, it means it's a thread
                if other_conn.parentId:
                    await webhook.send(
                        message.content,
                        username=f"{message.author.name} | {message.guild.name}",
                        avatar_url=message.author.display_avatar.url,
                        thread=discord.Object(id=other_conn.channelId),
                    )
                else:
                # Send the message using the webhook
                    await webhook.send(
                        message.content,
                        username=f"{message.author.name} | {message.guild.name}",
                        avatar_url=message.author.display_avatar.url,
                    )

        logger.info(
            f"Broadcasted message from {message.author} to {len(other_connections)} channels in hub {hub.id}"
        )


async def setup(bot):
    await bot.add_cog(OnMessageCreate(bot))
