import discord
from discord.ext import commands
from zuid import ZUID
import sentry_sdk as sdk

from utils.modules.errors import check_errors, custom_discord
from utils.modules.ui.CreateEmbed import get_text
from utils.constants import InterchatConstants

constants = InterchatConstants()

async def error_handler(bot, source, error):
    error_id = ZUID(prefix='error_', length=10)
    error_id = error_id()
    embed = discord.Embed(title='Error!', description=' ', color=discord.Color.red())

    is_interaction = isinstance(source, discord.Interaction)
    user = source.user if is_interaction else source.author
    guild = source.guild

    if isinstance(error, commands.MissingRequiredArgument):
        embed.description = f"{bot.emotes.x_icon} Missing argument: `{error.param.name}`."

    elif isinstance(error, commands.BadArgument):
        embed.description = f"{bot.emotes.x_icon} Invalid input. Please try again."

    elif isinstance(error, discord.NotFound):
        embed.description = f"{bot.emotes.x_icon} Asset not found. Pleae Check my permissions."

    elif isinstance(error, discord.Forbidden):
        embed.description = f"{bot.emotes.x_icon} I don’t have permission to do that."

    elif isinstance(error, discord.HTTPException) and error.code == 10062:
        embed.description = f"{bot.emotes.x_icon} Discord couldn’t process that. Try again."

    elif isinstance(error, commands.MissingPermissions):
        embed.title = "Insufficient Permissions"
        embed.description = f"{bot.emotes.x_icon} You don’t have permission to do this."

    elif isinstance(error, custom_discord.InteractionCheck):
        embed.description = f"{bot.emotes.x_icon} You may not use this interaction."

    elif isinstance(error, custom_discord.RateLimited):
        embed.description = f"{bot.emotes.x_icon} You're being rate limited. Slow down."

    elif isinstance(error, (commands.CheckFailure, commands.CommandNotFound)):
        return

    else:
        embed.description = f"{bot.emotes.x_icon} Uh oh! An unexpected error occurred."
        embed.add_field(name='Error ID', value=f'`{error_id}`', inline=True)

        if constants.environment().lower() == 'production':
            with sdk.push_scope() as scope:
                if guild:
                    scope.set_tag('guild_id', guild.id)
                scope.set_tag('error_id', error_id)
                scope.level = error
                sdk.capture_exception(error)

        dev_embed = discord.Embed(title='Error', description=f'{error}', color=discord.Color.red())
        dev_embed.add_field(name="Error ID", value=f"```{error_id}```", inline=False)
        dev_embed.add_field(name="User", value=f"{user.mention} `{user.id}`", inline=False)

        if guild:
            dev_embed.add_field(name="Guild", value=f"{guild.name} `{guild.id}`", inline=False)

        if not is_interaction and hasattr(source, "command"):
            dev_embed.add_field(name="Command", value=f"{source.command}", inline=False)

        dev_guild = bot.get_guild(1390620814478544946) or bot.get_guild(1390620814478544946)
        if dev_guild:
            channel = discord.utils.get(dev_guild.text_channels, id=1390620815199965223)
            if channel:
                await channel.send(embed=dev_embed)

    try:
        if is_interaction:
            if not source.response.is_done():
                await source.response.send_message(embed=embed, ephemeral=True)
            else:
                await source.followup.send(embed=embed, ephemeral=True)
        else:
            await source.send(embed=embed)
    except discord.Forbidden:
        try:
            await user.send(embed=embed)
        except:
            pass