import discord
from discord.ext import commands
from discord.ui import View, button, Button

from sqlalchemy import select

from utils.modules.ui.CreateEmbed import create_embed, get_text
from utils.modules.core.db.models import User, Hub
from utils.modules.errors import custom_discord, check_errors
from utils.modules.errors.error_handler import error_handler

class RuleButtons(View):
    def __init__(self, bot, user):
        super().__init__(timeout=300)
        self.bot = bot
        self.user = user

        self.setup_buttons()

    def setup_buttons(self):
        self.accept_callback.emoji = self.bot.emotes.tick_icon
        self.decline_callback.emoji = self.bot.emotes.x_icon 

    @button(
        emoji='❓',
        label='Accept',
        style=discord.ButtonStyle.green
    )
    async def accept_callback(self, interaction: discord.Interaction, button: Button):
        ...

    @button(
        emoji='❓',
        label='Decline',
        style=discord.ButtonStyle.grey
    )
    async def decline_callback(self, interaction: discord.Interaction, button: Button):
        ...

async def has_accepted(interaction):
    async with interaction.bot.db.get_session() as session:
        stmt = (select(Hub).where(Hub.rulesAcceptances.any(User.id == str(interaction.user.id))))
        result = (await session.execute(stmt)).scalars().all()

    if not result:
        return False
    
    return True

async def send_rules(ctx, hub): # CHANGE ME TO INTERACTION
    async with ctx.bot.db.get_session() as session:
        stmt = (select(Hub.rules).where(Hub.name == hub))
        rules = (await session.execute(stmt)).scalars().all()

    if rules == []:
        rules = get_text('en', 'rules.rules', guidelines_link='https://www.interchat.tech/guidelines')

    user = ctx.author
    embed = discord.Embed(title=f'{hub} Rules', description=rules, color=0x9172D8) # hub.name + change color to fit constants
    embed.set_author(name=f"@{user.name}", icon_url=user.display_avatar.url)
    embed.set_footer(text=f"Hub: {hub}") # hub.name
    view = RuleButtons(ctx.bot, ctx.author)

    await ctx.send(embed=embed, view=view, ephemeral=True)