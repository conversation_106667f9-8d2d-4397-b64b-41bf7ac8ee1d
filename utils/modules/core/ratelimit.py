import discord
from discord.ext import commands

from utils.constants import InterchatConstants, redis_client
from utils.modules.errors.custom_discord import RateLimited
from utils.modules.errors.error_handler import error_handler

async def message_rate_limit(ctx):
    constants = InterchatConstants()

    limit = constants.rate_limits()['limit']
    period = constants.rate_limits()['period']

    key = f'rate_limit:{ctx.guild.id}:{ctx.author.id}'
    count = await redis_client.incr(key)

    if count == 1:
        await redis_client.expire(key, period)
    
    if count > limit: 
        raise RateLimited()
    
    return True