import discord
from discord.ext import commands

from utils.modules.errors import custom_discord, check_errors
from utils.modules.errors.error_handler import error_handler


async def interaction_check(interaction, invoked: discord.User, interacted: discord.User | discord.Member) -> bool:
    if invoked.id != interacted.id:
        try:
            raise custom_discord.InteractionCheck()
        except Exception as e:
            await error_handler(interaction.client, interaction, e)
        return False
    return True


async def is_interchat_staff(ctx, user: discord.User | None = None):
    if hasattr(ctx, 'bot'):
        bot = ctx.bot
        current_user = ctx.author
    else:
        bot = ctx.bot
        current_user = ctx.user
    
    guild = await bot.fetch_guild(1390620814478544946)
    target_user_id = user.id if user is not None else current_user.id
    
    try:
        member = await guild.fetch_member(target_user_id)
        return any(role.id == 1390620814587723782 for role in member.roles)
    except discord.NotFound:
        return False


def is_interchat_staff_check():
    async def predicate(ctx: commands.Context):
        return await is_interchat_staff(ctx)

    return commands.check(predicate)


async def permission_check(level):
    ...
