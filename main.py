from tracemalloc import start
import discord
from discord.ext import commands
from cogwatch import watch
import time
import os
import sys
import asyncio
from datetime import datetime

from sqlalchemy import select

from utils.modules.core.db.models import ServerData
from utils.modules.emojis.EmojiManager import EmojiManager
from utils.utils import chunk_guilds
from utils.modules.core.ratelimit import message_rate_limit
from utils.constants import logger, InterchatConstants
from utils.modules.core.db.database import Database, init_database

constants = InterchatConstants()

if constants.environment().lower() == 'production':
    import sentry_sdk

    sentry_sdk.init(
        dsn=constants.sentry_dsn(),
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
        send_default_pii=True
    )

    presence = 'Building bridges for your servers'

else:
    presence = 'In development'


class Bot(commands.AutoShardedBot):
    def __init__(self):
        intent = discord.Intents.default()
        intent.message_content = True
        intent.members = True

        super().__init__(
            command_prefix=constants.prefix(),
            intents=intent,
            chunk_guilds_at_startup=False,
            help_command=None,
            reconnect=True
        )

        self.start_time = datetime.now()
        self.before_invoke(self.before_commands)
        self.after_invoke(self.after_commands)
        self.guilds_chunked = asyncio.Event()
        self.db: Database
        self.uptime = 0
        self.emotes: EmojiManager

    async def before_commands(self, ctx: commands.Context[commands.Bot]):
        await bot.wait_until_ready()
        if ctx.guild and not ctx.guild.chunked:
            await ctx.guild.chunk(cache=True)

        await message_rate_limit(ctx)
    async def after_commands(self, ctx: commands.Context[commands.Bot]):
        # store server in db if not already exists
        if not ctx.guild:
            return
        async with self.db.get_session() as session:
            stmt = select(ServerData.id).where(ServerData.id == str(ctx.guild.id))
            existing = (await session.execute(stmt)).scalar()

            if not existing:
                server = ServerData(
                    id=str(ctx.guild.id),
                    name=ctx.guild.name,
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                    iconUrl=ctx.guild.icon.url if ctx.guild.icon else None,
                    lastMessageAt=datetime.now(),
                    inviteCode="abcd"
                )
                session.add(server)
                await session.commit()
                logger.info(f'Created new server: {server.name}')        

    async def is_owner(self, user: discord.abc.User) -> bool:

        if user.id in constants.auth_users():
            return True
        return False

    async def setup_hook(self):
        # Initialize database connection
        try:
            self.db = init_database(os.environ['DATABASE_URL'])
            # Uncomment the line below if you want to create tables automatically
            await self.db.create_tables()

            logger.info('Database initialized successfully')
        except Exception as e:
            logger.error(f'Failed to initialize database: {e}')
            raise

        count = 0
        for root, _, files in os.walk('./cogs'):
            for file in files:
                if file.endswith('.py'):
                    cog_path = os.path.relpath(os.path.join(root, file), './cogs')
                    cog_module = cog_path.replace(os.sep, '.')[:-3]

                    try:
                        await bot.load_extension(f'cogs.{cog_module}')
                        count += 1
                        logger.info(f'{cog_module} loaded successfully')
                    except Exception as e:
                        logger.error(f'{cog_module} failed to load: {e}')

        logger.info(f'Successfully loaded {count} cog(s).')
        await bot.change_presence(activity=discord.CustomActivity(name=presence))

    async def on_connect(self):
        logger.info('Connected to discord gateway')

    async def on_disconnect(self):
        logger.warning('Disconnected from discord gateway')

    async def on_shard_connect(self, shard_id: int):
        await self.tree.sync()
        logger.info(f'Shard {shard_id} has connected to discord gateway')

    async def on_shard_disconnected(self, shard_id: int):
        logger.info(f'Shard {shard_id} has disconnected from discord gatweway')

    @watch(path='cogs', preload=False)
    async def on_ready(self):
        bot.uptime = int(time.time())
        await bot.change_presence(activity=discord.CustomActivity(name=presence))

        logger.info(f'{self.user} has started successfully.')

        await chunk_guilds(self.guilds)

        self.emotes = EmojiManager()
        await self.emotes.load(self)

        self.emotes.refresh_icon


bot = Bot()


async def start_bot():
    max_retries = 10
    retry_delay = 5
    retries = 0

    while retries < max_retries:
        try:
            logger.info(f'Starting bot... (Attempt {retries + 1})')
            await bot.start(constants.token())
        except (OSError, TimeoutError) as e:
            retries += 1
            logger.error(f'Connection error occured, thrown error: {e}')

            if retries < max_retries:
                logger.info(f'Retrying in {retry_delay} seconds...')
                await asyncio.sleep(retry_delay)
            else:
                break

        except Exception as e:
            logger.critical(f'Unexpected erorr occured. {e}')
            sys.exit('FAILED TO START: UNEXPECTED ERROR')
    logger.critical('Max retries reached. Cancelling operation.')
    sys.exit('FAILED TO START: MAX RETRIES')


if __name__ == '__main__':
    try:
        asyncio.run(start_bot())
    except KeyboardInterrupt:
        logger.warning('Bot shutting down...')
        sys.exit(0)
